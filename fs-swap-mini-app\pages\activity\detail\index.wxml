<!--pages/activity/detail/index.wxml-->
<view class="container">
  <view wx:if="{{loading}}" class="loading-container">
    <van-loading size="24px" color="#3B7FFF">加载中...</van-loading>
  </view>

  <block wx:elif="{{activity}}">
    <!-- 活动图片轮播 -->
    <view class="activity-cover">
      <swiper wx:if="{{activity.imageUrls && activity.imageUrls.length > 0}}" class="swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}">
        <swiper-item wx:for="{{activity.imageUrls}}" wx:key="*this">
          <image src="{{item}}" mode="aspectFill" class="cover-image" binderror="onImageError" data-index="{{index}}" bindtap="previewImage" data-url="{{item}}" data-urls="{{activity.imageUrls}}" />
        </swiper-item>
      </swiper>
      <image wx:else src="/static/img/activity.png" mode="aspectFill" class="cover-image" bindtap="previewImage" data-url="/static/img/activity.png" data-urls="{{['/static/img/activity.png']}}" />
      <view class="activity-status">
        <van-tag type="{{activity.status === '1' ? 'primary' : 'warning'}}">{{activity.statusName}}</van-tag>
      </view>
    </view>

<!-- 活动描述 -->
    <view class="activity-section">
      <view class="section-title">{{activity.title}}</view>
      <view class="activity-description">{{activity.description}}</view>
    </view>
    <!-- 活动基本信息 -->
    <view class="activity-info">
      <!-- <view class="activity-title">{{activity.title}}</view> -->
      <view class="activity-meta">
        <view class="meta-item">
          <van-icon name="clock-o" size="16px" color="#3B7FFF" />
          <text>开始时间：{{activity.startTime}}</text>
        </view>
        <view class="meta-item">
          <van-icon name="clock-o" size="16px" color="#3B7FFF" />
          <text>结束时间：{{activity.endTime}}</text>
        </view>

        <view class="meta-item" wx:if="{{!activity.location}}">
          <van-icon name="location-o" size="16px" color="#3B7FFF" />
          <text>活动地点：{{activity.address}}</text>
        </view>

        <!-- 地图显示 -->
        <view class="location-map-container" wx:if="{{activity.location}}">
          <map
            id="activityMap"
            class="location-map"
            longitude="{{mapInfo.longitude}}"
            latitude="{{mapInfo.latitude}}"
            markers="{{mapInfo.markers}}"
            scale="16"
            show-compass
            enable-scroll="true"
            enable-rotate="true"
            enable-zoom="true"
            bindtap="onMapTap"
            catchtouchmove="onMapTouchMove"
          ></map>
          <view class="map-overlay" bindtap="openLocation">查看导航</view>
        </view>

        <view class="meta-item" bindtap="openLocation" wx:if="{{activity.location}}">
          <van-icon name="location-o" size="16px" color="#3B7FFF" />
          <text>活动地点：{{activity.address}}</text>
          <view class="location-btn">
            <van-icon name="guide-o" size="16px" />
          </view>
        </view>
        <view class="meta-item">
          <van-icon name="friends-o" size="16px" color="#3B7FFF" />
          <text>已报名：{{activity.signupCount || 0}}人</text>
          <text wx:if="{{activity.maxParticipants}}">（限{{activity.maxParticipants}}人）</text>
        </view>
        <view class="meta-item">
          <van-icon name="label-o" size="16px" color="#3B7FFF" />
          <text>活动分类：{{activity.categoryName}}</text>
        </view>
        <view class="meta-item">
          <van-icon name="gold-coin-o" size="16px" color="#3B7FFF" />
          <text>活动费用：{{activity.isFree === 'Y' ? activity.feeAmount + '元' : '免费'}}</text>
        </view>
        <view class="meta-item" wx:if="{{activity.isFree === 'Y' && activity.feeDescription}}">
          <van-icon name="info-o" size="16px" color="#3B7FFF" />
          <text>费用说明：{{activity.feeDescription}}</text>
        </view>
        <view class="meta-item">
          <van-icon name="underway-o" size="16px" color="#3B7FFF" />
          <text>报名时间：{{activity.signupStartTime}} 至 {{activity.signupEndTime}}</text>
        </view>
      </view>

      <!-- 发布者信息 -->
      <view class="organizer-info">
        <image src="{{activity.avatar || '/static/img/default_avatar.png'}}" class="organizer-avatar" bindtap="navigateToUserProfile" data-user-id="{{activity.userId}}" />
        <view class="organizer-name" bindtap="navigateToUserProfile" data-user-id="{{activity.userId}}">{{activity.nickname}}</view>
        <view class="organizer-time">发布于 {{activity.createTime}}</view>
      </view>
    </view>
    <!-- 报名列表 -->
    <view class="activity-section">
      <view class="section-title">报名列表</view>
      <view class="signup-list">
        <view wx:if="{{signups.length === 0 && !signupLoading}}" class="empty-comment">
          暂无报名记录
        </view>

        <view wx:for="{{signups}}" wx:key="id" class="signup-item">
          <view class="signup-user">
            <image src="{{item.avatar || '/static/img/default_avatar.png'}}" class="signup-avatar" bindtap="navigateToUserProfile" data-user-id="{{item.userId}}" />
            <view class="signup-info">
              <view class="signup-name" bindtap="navigateToUserProfile" data-user-id="{{item.userId}}">{{item.nickname}}</view>
              <view class="signup-time">报名时间：{{item.signupTime}}</view>

              <view class="signup-additional" wx:if="{{item.additionalInfo && isOwner}}">附加信息：{{item.additionalInfo}}</view>
            </view>
          </view>
          <view class="signup-actions" wx:if="{{isOwner}}">
            <block wx:if="{{item.status === '0'}}">
              <van-button size="mini" type="primary" bindtap="auditSignup" data-id="{{item.id}}" data-status="1">通过</van-button>
              <van-button size="mini" type="danger" bindtap="auditSignup" data-id="{{item.id}}" data-status="2">拒绝</van-button>
            </block>
            <block wx:if="{{item.status === '1'}}">
              <van-button size="mini" type="info" bindtap="contactSignupUser" data-id="{{item.id}}">联系Ta</van-button>
            </block>
          </view>
        </view>

        <!-- 加载更多 -->
        <view wx:if="{{signupLoading}}" class="loading-more">
          <van-loading size="24px" color="#3B7FFF">加载中...</van-loading>
        </view>
        <view wx:if="{{!hasMoreSignups && signups.length > 0}}" class="no-more">没有更多报名了</view>
      </view>
    </view>

    <!-- 评论列表 -->
    <view class="activity-section">
      <view class="section-title">评论区</view>
      <view class="comment-list">
        <view wx:if="{{comments.length === 0 && !commentLoading}}" class="empty-comment">
          暂无评论，快来发表第一条评论吧
        </view>

        <view wx:for="{{comments}}" wx:key="id" class="comment-item">
          <view class="comment-user">
            <image src="{{item.avatar || '/static/img/default_avatar.png'}}" class="comment-avatar" bindtap="navigateToUserProfile" data-user-id="{{item.userId}}" />
            <view class="comment-info">
              <view class="comment-name" bindtap="navigateToUserProfile" data-user-id="{{item.userId}}">{{item.nickname}}</view>
              <view class="comment-time">{{item.createTime}}</view>
            </view>
          </view>
          <view class="comment-content">
            <text wx:if="{{item.parentNickname}}" class="reply-to">回复 {{item.parentNickname}}：</text>
            {{item.content}}
          </view>
          <view class="comment-actions">
            <view class="comment-reply" bindtap="showCommentInput" data-id="{{item.id}}" data-name="{{item.nickname}}">
              <van-icon name="comment-o" size="14px" />
              <text>回复</text>
            </view>
            <view wx:if="{{item.isMine}}" class="comment-delete" bindtap="deleteComment" data-id="{{item.id}}">
              <van-icon name="delete-o" size="14px" />
              <text>删除</text>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view wx:if="{{commentLoading}}" class="loading-more">
          <van-loading size="24px" color="#3B7FFF">加载中...</van-loading>
        </view>
        <view wx:if="{{!hasMoreComments && comments.length > 0}}" class="no-more">没有更多评论了</view>
      </view>

      <!-- 评论按钮 -->
      <view class="comment-button" bindtap="showCommentInput">
        <van-icon name="comment-o" size="16px" />
        <text>写评论</text>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <!-- 活动发布者操作 -->
      <block wx:if="{{isOwner}}">
        <view class="action-buttons owner-actions">
          <van-button wx:if="{{activity.status === '0'}}" type="primary" size="normal" custom-class="action-btn edit-btn" bindtap="editActivity">
            <van-icon name="edit" size="18px" custom-class="btn-icon" />
            <text>编辑活动</text>
          </van-button>
          <van-button wx:if="{{activity.status === '1'}}" type="warning" size="normal" custom-class="action-btn cancel-btn" bindtap="cancelActivity">
            <van-icon name="close" size="18px" custom-class="btn-icon" />
            <text>取消活动</text>
          </van-button>
          <van-button wx:if="{{activity.status === '0' || activity.status === '3'}}" type="danger" size="normal" custom-class="action-btn delete-btn" bindtap="deleteActivity">
            <van-icon name="delete-o" size="18px" custom-class="btn-icon" />
            <text>删除活动</text>
          </van-button>
        </view>
      </block>

      <!-- 普通用户操作 -->
      <block wx:else>
        <view class="action-buttons user-actions">
          <van-button
            wx:if="{{!isSignedUp && activity.status === '1'}}"
            type="primary"
            size="large"
            custom-class="action-btn signup-btn"
            bindtap="showSignupPopup">
            <van-icon name="sign" size="20px" custom-class="btn-icon" />
            <text>立即报名</text>
          </van-button>

          <view class="dual-buttons" wx:if="{{isSignedUp || activity.status !== '1'}}">
            <van-button
              wx:if="{{isSignedUp}}"
              type="warning"
              size="normal"
              custom-class="action-btn cancel-signup-btn"
              bindtap="cancelSignup">
              <van-icon name="close" size="18px" custom-class="btn-icon" />
              <text>取消报名</text>
            </van-button>

            <van-button
              wx:if="{{activity.status !== '1' || (isSignedUp && activity.status === '1')}}"
              type="info"
              size="normal"
              custom-class="action-btn contact-btn"
              bindtap="contactOrganizer">
              <van-icon name="phone-o" size="18px" custom-class="btn-icon" />
              <text>联系发布者</text>
            </van-button>
          </view>
        </view>
      </block>
    </view>
  </block>

  <view wx:else class="error-container">
    <van-icon name="warning-o" size="48px" color="#cccccc" />
    <text>活动不存在或已被删除</text>
  </view>

  <!-- 评论输入弹窗 -->
  <van-popup
    show="{{showCommentInput}}"
    position="bottom"
    custom-style="height: 30%;"
    bind:close="hideCommentInput"
  >
    <view class="comment-popup">
      <view class="popup-header">
        <text>{{replyToName ? '回复 ' + replyToName : '发表评论'}}</text>
        <van-icon name="cross" bindtap="hideCommentInput" />
      </view>
      <van-field
        value="{{commentContent}}"
        placeholder="{{replyToName ? '回复 ' + replyToName : '说点什么吧...'}}"
        bind:change="onCommentChange"
        maxlength="200"
        show-word-limit
        autosize
        type="textarea"
      />
      <view class="popup-footer">
        <van-button type="primary" size="small" bindtap="submitComment" disabled="{{!commentContent}}">发送</van-button>
      </view>
    </view>
  </van-popup>

  <!-- 报名弹窗 -->
  <van-popup
    show="{{showSignupPopup}}"
    position="bottom"
    custom-style="height: 40%;"
    bind:close="hideSignupPopup"
  >
    <view class="signup-popup">
      <view class="popup-header">
        <text>活动报名</text>
        <van-icon name="cross" bindtap="hideSignupPopup" />
      </view>

      <van-field
        value="{{additionalInfo}}"
        label="附加信息"
        placeholder="请输入其他需要说明的信息"
        bind:change="onAdditionalInfoChange"
        type="textarea"
        autosize
      />
      <view class="popup-footer">
        <van-button type="primary" size="normal" block bindtap="submitSignup">确认报名</van-button>
      </view>
    </view>
  </van-popup>

  <!-- 联系方式弹窗组件 -->
  <contact-modal
    show="{{showContactModal}}"
    targetNickname="{{targetNickname}}"
    contacts="{{contacts}}"
    fileUrl="{{fileUrl}}"
    bind:close="closeContactModal"
  ></contact-modal>

  <!-- 登录组件 -->
  <login-action id="loginAction" bind:loginSuccess="onLoginSuccess" bind:loginFail="onLoginFail"></login-action>

  <!-- 小区认证弹框 -->
  <residential-auth id="residentialAuth" bind:confirm="onConfirmResidentialAuth" bind:close="onCloseResidentialAuth" />
</view>
