package com.fs.swap.wx.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fs.swap.common.constant.CacheConstants;
import com.fs.swap.common.core.controller.WxApiBaseController;
import com.fs.swap.common.core.domain.AjaxResult;
import com.fs.swap.common.core.domain.entity.CommunityHelp;
import com.fs.swap.common.core.domain.entity.Product;
import com.fs.swap.common.core.domain.entity.TradeOrder;
import com.fs.swap.common.core.domain.entity.UserInfo;
import com.fs.swap.common.core.domain.entity.UserContact;

import com.fs.swap.common.core.page.TableDataInfo;
import com.fs.swap.common.core.redis.RedisCache;
import com.fs.swap.common.enums.*;
import com.fs.swap.common.exception.ServiceException;
import com.fs.swap.common.utils.DateUtils;
import com.fs.swap.common.utils.JacksonUtil;
import com.fs.swap.common.utils.OrderUtils;
import com.fs.swap.system.service.*;
import com.fs.swap.common.annotation.RepeatSubmit;
import com.fs.swap.wx.mapstruct.OrderConvert;
import com.fs.swap.wx.pojo.dto.CreateOrderDTO;
import com.fs.swap.wx.pojo.vo.OrderDetailVO;
import com.fs.swap.wx.service.TaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 订单
 */
@RestController
@RequestMapping("/order")
public class OrderController extends WxApiBaseController {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ITradeOrderService tradeOrderService;

    @Autowired
    private IProductService productService;

    @Autowired
    private IUserInfoService userInfoService;

    @Autowired
    private IUserContactService userContactService;

    @Autowired
    private OrderConvert orderConvert;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private TaskService taskService;
    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private ICommunityHelpService communityHelpService;

    /**
     * 创建订单
     */
    @PostMapping("/create")
    @Transactional
    public AjaxResult createProductOrder(@RequestBody CreateOrderDTO createOrderDTO) {
        // 获取当前登录用户ID
        Long buyerId = getUserId();

        // 获取商品信息
        Product product = productService.selectProductById(createOrderDTO.getBusinessId());
        if (product == null) {
            throw new ServiceException(ErrorType.E_5012);
        }

        // 检查商品状态是否为在售
        if (!ProductStatus.ON_SALE.getCode().equals(product.getStatus())) {
            throw new ServiceException(ErrorType.E_6001);
        }

        // 检查买家是否为卖家本人
        if (buyerId.equals(product.getUserId())) {
            throw new ServiceException(ErrorType.E_6002);
        }

        // 获取买家信息，检查积分是否足够
        UserInfo buyer = userInfoService.selectUserInfoById(buyerId);
        if (buyer == null) {
            throw new ServiceException(ErrorType.E_6003);
        }
        String needSilver = sysConfigService.selectConfigByKey("swap.product.silver");

        // 检查买碳豆是否足够
        if (buyer.getSilver() < Long.parseLong(needSilver)) {
            throw new ServiceException(ErrorType.E_5009);
        }

        // 使用Redis分布式锁控制并发，锁的键使用商品ID
        String lockKey = CacheConstants.PRODUCT_LOCK_KEY + product.getId();

        // 使用分布式锁执行下单操作，锁过期时间设置为10秒
        // 如果获取锁失败，抛出异常提示用户稍后重试
        ObjectNode resultJson = JacksonUtil.creatObjNode();

        try {
            // 使用nxLockWithRemove方法，在执行完成后自动释放锁
            redisCache.nxLockWithRemove(lockKey, 10, () -> {
                // 再次检查商品状态是否为在售（双重检查，防止锁等待期间状态被其他线程修改）
                Product currentProduct = productService.selectProductById(createOrderDTO.getBusinessId());
                if (currentProduct == null || !ProductStatus.ON_SALE.getCode().equals(currentProduct.getStatus())) {
                    throw new ServiceException(ErrorType.E_6001);
                }

                // 生成订单号
                String orderNo = OrderUtils.generateOrderNo();

                // 创建订单
                TradeOrder order = new TradeOrder();
                order.setOrderNo(orderNo);
                // 设置订单类型为商品订单
                order.setOrderType(OrderType.PRODUCT.getCode());
                order.setBuyerId(buyerId);
                order.setSellerId(currentProduct.getUserId());
                order.setBusinessId(currentProduct.getId());
                order.setPoints(currentProduct.getPrice().longValue());
                // 设置订单状态为联系
                order.setStatus(OrderStatus.CONTACT.getCode());
                order.setDeleted(0);
                order.setCreateTime(DateUtils.getNowDate());

                // 保存订单
                int result = tradeOrderService.insertTradeOrder(order);
                if (result <= 0) {
                    throw new ServiceException(ErrorType.E_6015);
                }
                // 扣除买家碳豆
                userInfoService.deductSilverAndRecord(buyerId, Long.parseLong(needSilver), SilverEventType.PRODUCT_BUY);

                // 更新商品状态为已售
                currentProduct.setStatus(ProductStatus.SOLD.getCode());
                currentProduct.setBuyerId(buyerId);
                productService.updateProduct(currentProduct);

                // 触发购买商品任务事件
                try {
                    taskService.processTaskEvent(buyerId,
                            TaskEventType.PRODUCT_BUY.getCode(),
                            order.getId().toString());
                } catch (Exception e) {
                    // 任务事件处理失败不影响主业务
                    logger.warn("处理购买商品任务事件失败", e);
                }

                // 设置返回信息
                resultJson.put("orderId", order.getId());
                resultJson.put("orderNo", orderNo);
            });

            return AjaxResult.success(resultJson);
        } catch (ServiceException e) {
            // 如果是业务异常，直接抛出
            throw e;
        } catch (Exception e) {
            // 记录异常日志
            logger.error("创建订单时发生异常，商品ID: {}, 买家ID: {}, 异常信息: {}",
                    product.getId(), buyerId, e.getMessage(), e);

            // 返回友好的错误信息
            return AjaxResult.error("创建订单失败，请稍后重试");
        }
    }

    /**
     * 获取订单详情
     */
    @GetMapping("/detail/{orderId}")
    public AjaxResult getOrderDetail(@PathVariable Long orderId) {
        // 获取当前登录用户ID
        Long userId = getUserId();

        // 获取订单信息
        TradeOrder order = tradeOrderService.selectTradeOrderById(orderId);
        if (order == null) {
            throw new ServiceException(ErrorType.E_5012);
        }

        // 检查订单是否属于当前用户
        if (!userId.equals(order.getBuyerId()) && !userId.equals(order.getSellerId())) {
            throw new ServiceException(ErrorType.E_6004);
        }

        // 获取买家和卖家信息
        UserInfo buyer = userInfoService.selectUserInfoById(order.getBuyerId());
        UserInfo seller = userInfoService.selectUserInfoById(order.getSellerId());

        OrderDetailVO orderDetailVO;

        // 根据订单类型获取对应的业务信息
        if (OrderType.PRODUCT.val().equals(order.getOrderType())) {
            // 商品订单：获取商品信息
            Product product = productService.selectProductById(order.getBusinessId());
            orderDetailVO = orderConvert.toOrderDetailVO(order, product, buyer, seller);
        } else if (OrderType.COMMUNITY_HELP.val().equals(order.getOrderType())) {
            // 邻里互助订单：获取互助信息
            CommunityHelp communityHelp = communityHelpService.selectCommunityHelpById(order.getBusinessId());
            orderDetailVO = orderConvert.toOrderDetailVO(order, communityHelp, buyer, seller);
        } else {
            // 未知订单类型
            throw new ServiceException(ErrorType.E_5012);
        }

        return AjaxResult.success(orderDetailVO);
    }

    /**
     * 获取我的订单列表
     */
    @GetMapping("/my_orders")
    public TableDataInfo getMyOrders(@RequestParam(required = false) String status,
                                     @RequestParam(required = false, defaultValue = UserRole.BUYER_CODE) String role) {
        // 获取当前登录用户ID
        Long userId = getUserId();

        // 创建查询条件
        TradeOrder queryOrder = new TradeOrder();

        // 根据角色设置查询条件
        if (UserRole.BUYER.getCode().equals(role)) {
            queryOrder.setBuyerId(userId);
        } else if (UserRole.SELLER.getCode().equals(role)) {
            queryOrder.setSellerId(userId);
        } else {
            throw new ServiceException(ErrorType.E_6016);
        }

        // 设置订单状态查询条件
        if (status != null) {
            queryOrder.setStatus(status);
        }

        // 不限制订单类型，支持商品订单和邻里互助订单

        // 设置未删除条件
        queryOrder.setDeleted(0);

        // 分页查询
        startPage();
        List<TradeOrder> orderList = tradeOrderService.selectTradeOrderList(queryOrder);

        // 获取相关的商品、买家和卖家信息
        if (!orderList.isEmpty()) {
            // 按订单类型分组
            Map<String, List<TradeOrder>> ordersByType = orderList.stream()
                    .collect(Collectors.groupingBy(TradeOrder::getOrderType));

            // 收集所有用户ID
            Set<Long> userIds = new HashSet<>();
            orderList.forEach(order -> {
                if (order.getBuyerId() != null) {
                    userIds.add(order.getBuyerId());
                }
                if (order.getSellerId() != null) {
                    userIds.add(order.getSellerId());
                }
            });

            // 批量查询用户信息
            Map<Long, UserInfo> userMap = userInfoService.batchSelectUserInfoByIds(userIds);

            List<OrderDetailVO> orderVOList = new ArrayList<>();

            // 处理商品订单
            List<TradeOrder> productOrders = ordersByType.get(OrderType.PRODUCT.val());
            if (productOrders != null && !productOrders.isEmpty()) {
                // 收集商品ID
                Set<Long> productIds = productOrders.stream()
                        .map(TradeOrder::getBusinessId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());

                // 批量查询商品信息
                List<Product> products = new ArrayList<>();
                for (Long productId : productIds) {
                    Product product = productService.selectProductById(productId);
                    if (product != null) {
                        products.add(product);
                    }
                }
                Map<Long, Product> productMap = products.stream()
                        .collect(Collectors.toMap(Product::getId, product -> product));

                // 转换商品订单
                List<OrderDetailVO> productOrderVOs = productOrders.stream().map(order -> {
                    Product product = productMap.get(order.getBusinessId());
                    UserInfo buyer = userMap.get(order.getBuyerId());
                    UserInfo seller = userMap.get(order.getSellerId());

                    return orderConvert.toOrderDetailVO(order, product, buyer, seller);
                }).collect(Collectors.toList());

                orderVOList.addAll(productOrderVOs);
            }

            // 处理邻里互助订单
            List<TradeOrder> helpOrders = ordersByType.get(OrderType.COMMUNITY_HELP.val());
            if (helpOrders != null && !helpOrders.isEmpty()) {
                // 收集邻里互助ID
                Set<Long> helpIds = helpOrders.stream()
                        .map(TradeOrder::getBusinessId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());

                // 批量查询邻里互助信息
                Map<Long, CommunityHelp> helpMap = new HashMap<>();
                for (Long helpId : helpIds) {
                    CommunityHelp help = communityHelpService.selectCommunityHelpById(helpId);
                    if (help != null) {
                        helpMap.put(helpId, help);
                    }
                }

                // 转换邻里互助订单
                List<OrderDetailVO> helpOrderVOs = helpOrders.stream().map(order -> {
                    CommunityHelp help = helpMap.get(order.getBusinessId());
                    UserInfo buyer = userMap.get(order.getBuyerId());
                    UserInfo seller = userMap.get(order.getSellerId());

                    return orderConvert.toOrderDetailVO(order, help, buyer, seller);
                }).collect(Collectors.toList());

                orderVOList.addAll(helpOrderVOs);
            }

            // 按创建时间倒序排列
            orderVOList.sort((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime()));

            return getDataTable(orderVOList);
        }

        return getDataTable(Collections.emptyList());
    }

    /**
     * 确认收货
     */
    @PostMapping("/confirm/{orderId}")
    @Transactional
    public AjaxResult confirmOrder(@PathVariable Long orderId) {
        // 获取当前登录用户ID
        Long userId = getUserId();

        // 获取订单信息
        TradeOrder order = tradeOrderService.selectTradeOrderById(orderId);
        if (order == null) {
            throw new ServiceException(ErrorType.E_5012);
        }

        // 检查是否为买家或者卖家
        if (!userId.equals(order.getBuyerId()) && !userId.equals(order.getSellerId())) {
            throw new ServiceException(ErrorType.E_6004);
        }

        // 检查订单状态是否为待确认
        if (!OrderStatus.PENDING.getCode().equals(order.getStatus())) {
            throw new ServiceException(ErrorType.E_6006);
        }

        // 更新订单状态为已完成
        order.setStatus(OrderStatus.COMPLETED.getCode());
        order.setCompleteTime(DateUtils.getNowDate());
        int result = tradeOrderService.updateTradeOrder(order);
        if (result <= 0) {
            throw new ServiceException(ErrorType.E_6017);
        }

        if (OrderType.PRODUCT.val().equals(order.getOrderType())) {
            // 给卖家增加碳豆
//            userInfoService.addSilverAndRecord(order.getSellerId(), order.getPoints(), SilverEventType.PRODUCT_SELL);

            // 触发卖出商品任务事件
            try {
                taskService.processTaskEvent(order.getSellerId(),
                        TaskEventType.PRODUCT_SELL.getCode(),
                        order.getId().toString());
            } catch (Exception e) {
                // 任务事件处理失败不影响主业务
                logger.warn("处理卖出商品任务事件失败", e);
            }
        }

        return AjaxResult.success("确认收货成功");
    }


    /**
     * 确认已经联系
     */
    @PostMapping("/contact/{orderId}")
    @Transactional
    public AjaxResult contactOrder(@PathVariable Long orderId) {
        // 获取当前登录用户ID
        Long userId = getUserId();

        // 获取订单信息
        TradeOrder order = tradeOrderService.selectTradeOrderById(orderId);
        if (order == null) {
            throw new ServiceException(ErrorType.E_5012);
        }

        // 检查是否为买家
        if (!userId.equals(order.getBuyerId())) {
            throw new ServiceException(ErrorType.E_6005);
        }

        // 检查订单状态是否为待联系
        if (!OrderStatus.CONTACT.getCode().equals(order.getStatus())) {
            throw new ServiceException(ErrorType.E_6021);
        }

        // 更新订单状态为待确认
        order.setStatus(OrderStatus.PENDING.getCode());
        order.setUpdateTime(DateUtils.getNowDate());
        int result = tradeOrderService.updateTradeOrder(order);
        if (result <= 0) {
            throw new ServiceException(ErrorType.E_5021);
        }

        return AjaxResult.success("更新成功");
    }

    /**
     * 取消订单
     */
    @PostMapping("/cancel/{orderId}")
    @Transactional
    public AjaxResult cancelOrder(@PathVariable Long orderId) {
        // 获取当前登录用户ID
        Long userId = getUserId();

        // 获取订单信息
        TradeOrder order = tradeOrderService.selectTradeOrderById(orderId);
        if (order == null) {
            throw new ServiceException(ErrorType.E_5012);
        }

        // 检查是否为买家
        if (!userId.equals(order.getBuyerId())) {
            throw new ServiceException(ErrorType.E_6007);
        }

        // 检查订单状态是否为待联系
        if (!OrderStatus.CONTACT.getCode().equals(order.getStatus())) {
            throw new ServiceException(ErrorType.E_6008);
        }

        // 更新订单状态为已取消
        order.setStatus(OrderStatus.CANCELLED.getCode());
        int result = tradeOrderService.updateTradeOrder(order);
        if (result <= 0) {
            throw new ServiceException(ErrorType.E_6018);
        }
        if (OrderType.PRODUCT.val().equals(order.getOrderType())) {
            userInfoService.addSilverAndRecord(order.getBuyerId(), order.getPoints(), SilverEventType.PRODUCT_CANCEL);
            // 将商品状态改回在售
            Product product = productService.selectProductById(order.getBusinessId());
            if (product != null) {
                product.setStatus(ProductStatus.ON_SALE.getCode());
                product.setBuyerId(null);
                productService.updateProduct(product);
            }
        }
        return AjaxResult.success("取消订单成功");
    }

    /**
     * 终止订单
     */
    @PostMapping("/termination/{orderId}")
    @Transactional
    public AjaxResult terminationOrder(@PathVariable Long orderId) {
        // 获取当前登录用户ID
        Long userId = getUserId();

        // 获取订单信息
        TradeOrder order = tradeOrderService.selectTradeOrderById(orderId);
        if (order == null) {
            throw new ServiceException(ErrorType.E_5012);
        }

        // 检查是否为买家或者卖家
        if (!userId.equals(order.getBuyerId()) && !userId.equals(order.getSellerId())) {
            throw new ServiceException(ErrorType.E_6004);
        }

        // 检查订单状态是否为待确认
        if (!OrderStatus.PENDING.getCode().equals(order.getStatus())) {
            throw new ServiceException(ErrorType.E_5021);
        }

        // 更新订单状态为已终止
        order.setStatus(OrderStatus.TERMINATION.getCode());
        int result = tradeOrderService.updateTradeOrder(order);
        if (result <= 0) {
            throw new ServiceException(ErrorType.E_500);
        }

        // 给买家增加碳豆 暂时不退回
//        userInfoService.addSilverAndRecord(order.getBuyerId(), order.getPoints(), SilverEventType.PRODUCT_CANCEL);

        return AjaxResult.success("操作成功");
    }

    /**
     * 申请退款
     */
    @PostMapping("/refund")
    @Transactional
    public AjaxResult applyRefund(@RequestParam Long orderId, @RequestParam String reason) {
        // 获取当前登录用户ID
        Long userId = getUserId();

        // 获取订单信息
        TradeOrder order = tradeOrderService.selectTradeOrderById(orderId);
        if (order == null) {
            throw new ServiceException(ErrorType.E_5012);
        }

        // 检查是否为买家
        if (!userId.equals(order.getBuyerId())) {
            throw new ServiceException(ErrorType.E_6009);
        }

        // 检查订单状态是否为待确认或已完成
        if (!OrderStatus.PENDING.getCode().equals(order.getStatus()) &&
                !OrderStatus.COMPLETED.getCode().equals(order.getStatus())) {
            throw new ServiceException(ErrorType.E_6010);
        }

        // 更新订单状态为退款中
        order.setStatus(OrderStatus.REFUNDING.getCode());
        order.setDisputeReason(reason);
        int result = tradeOrderService.updateTradeOrder(order);
        if (result <= 0) {
            throw new ServiceException(ErrorType.E_6019);
        }

        return AjaxResult.success("申请退款成功，等待卖家处理");
    }

    /**
     * 同意退款
     */
    @PostMapping("/agree_refund/{orderId}")
    @Transactional
    public AjaxResult agreeRefund(@PathVariable Long orderId) {
        // 获取当前登录用户ID
        Long userId = getUserId();

        // 获取订单信息
        TradeOrder order = tradeOrderService.selectTradeOrderById(orderId);
        if (order == null) {
            throw new ServiceException(ErrorType.E_5012);
        }

        // 检查是否为卖家
        if (!userId.equals(order.getSellerId())) {
            throw new ServiceException(ErrorType.E_6011);
        }

        // 检查订单状态是否为退款中
        if (!OrderStatus.REFUNDING.getCode().equals(order.getStatus())) {
            throw new ServiceException(ErrorType.E_6012);
        }

        // 更新订单状态为已退款
        order.setStatus(OrderStatus.REFUNDED.getCode());
        int result = tradeOrderService.updateTradeOrder(order);
        if (result <= 0) {
            throw new ServiceException(ErrorType.E_6020);
        }

        // 退还买家碳豆
        Long needSilver = Long.parseLong(sysConfigService.selectConfigByKey("swap.product.silver"));
        userInfoService.addSilverAndRecord(order.getBuyerId(), needSilver, SilverEventType.ORDER_REFUND);

        // 将商品状态改回在售
        Product product = productService.selectProductById(order.getBusinessId());
        if (product != null) {
            product.setStatus(ProductStatus.ON_SALE.getCode());
            product.setBuyerId(null);
            productService.updateProduct(product);
        }

        return AjaxResult.success("退款成功");
    }

    /**
     * 拒绝退款
     */
    @PostMapping("/reject_refund")
    @Transactional
    public AjaxResult rejectRefund(@RequestParam Long orderId, @RequestParam String reason) {
        // 获取当前登录用户ID
        Long userId = getUserId();

        // 获取订单信息
        TradeOrder order = tradeOrderService.selectTradeOrderById(orderId);
        if (order == null) {
            throw new ServiceException(ErrorType.E_5012);
        }

        // 检查是否为卖家
        if (!userId.equals(order.getSellerId())) {
            throw new ServiceException(ErrorType.E_6013);
        }

        // 检查订单状态是否为退款中
        if (!OrderStatus.REFUNDING.getCode().equals(order.getStatus())) {
            throw new ServiceException(ErrorType.E_6014);
        }

        // 更新订单状态为拒绝退款
        order.setStatus(OrderStatus.REFUND_REJECTED.getCode());
        order.setRemark(reason);
        int result = tradeOrderService.updateTradeOrder(order);
        if (result <= 0) {
            throw new ServiceException(ErrorType.E_6021);
        }

        return AjaxResult.success("已拒绝退款");
    }

    /**
     * 买家可以查看卖家的联系方式，
     */
    @GetMapping("/contact/{orderId}")
    public AjaxResult getOrderUserContact(@PathVariable Long orderId) {
        // 获取当前登录用户ID
        Long userId = getUserId();
        logger.info("获取联系方式请求，订单ID: {}, 用户ID: {}", orderId, userId);

        // 获取订单信息
        TradeOrder order = tradeOrderService.selectTradeOrderById(orderId);
        if (order == null) {
            logger.error("订单不存在，订单ID: {}", orderId);
            throw new ServiceException(ErrorType.E_5012);
        }

        // 检查订单是否属于当前买家
        if (!userId.equals(order.getBuyerId())) {
            logger.error("无权查看该订单，订单ID: {}, 用户ID: {}, 买家ID: {}, 卖家ID: {}",
                    orderId, userId, order.getBuyerId(), order.getSellerId());
            throw new ServiceException(ErrorType.E_6004);
        }

        List<JsonNode> visibleContacts = new ArrayList<>();

        // 根据订单类型获取联系方式
        if (OrderType.PRODUCT.val().equals(order.getOrderType())) {
            // 商品订单：获取商品中的联系方式
            Product product = productService.selectProductWithContactById(order.getBusinessId());
            if (product != null && product.getContactInfo() != null) {
                // 解析联系信息并过滤visible为true的数据
                JsonNode contactInfoNode = JacksonUtil.readTree(product.getContactInfo());
                if (contactInfoNode.isArray()) {
                    for (JsonNode contact : contactInfoNode) {
                        if (contact.has("visible") && contact.get("visible").asBoolean()) {
                            visibleContacts.add(contact);
                        }
                    }
                }
            }
        } else if (OrderType.COMMUNITY_HELP.val().equals(order.getOrderType())) {
            // 互助订单：获取发布者的联系方式
            List<UserContact> userContacts = userContactService.selectVisibleUserContactsByUserId(order.getSellerId());
            for (UserContact contact : userContacts) {
                ObjectNode contactNode = JacksonUtil.creatObjNode();
                contactNode.put("type", Integer.parseInt(contact.getContactType()));
                contactNode.put("value", contact.getContactValue());
                contactNode.put("visible", contact.getIsVisible());
                visibleContacts.add(contactNode);
            }
        }

        //未联系自动更新为已联系
        if (order.getStatus().equals(OrderStatus.CONTACT.val())) {
            order.setStatus(OrderStatus.PENDING.getCode());
            order.setUpdateTime(DateUtils.getNowDate());
            tradeOrderService.updateTradeOrder(order);
        }

        return AjaxResult.success(visibleContacts);
    }

    /**
     * 联系发布者（生成订单）
     */
    @PostMapping("/create/help")
    @RepeatSubmit
    public AjaxResult createHelpOrder(@RequestParam Long id) {
        // 获取当前登录用户ID
        Long buyerId = getUserId();

        // 获取互助信息
        CommunityHelp communityHelp = communityHelpService.selectCommunityHelpById(id);
        if (communityHelp == null) {
            throw new ServiceException(ErrorType.E_5012);
        }

        // 检查状态
        if (!ProductStatus.ON_SALE.val().equals(communityHelp.getStatus())) { // 1-正常
            throw new ServiceException(ErrorType.E_5012);
        }

        // 检查是否为发布者本人
        if (buyerId.equals(communityHelp.getUserId())) {
            throw new ServiceException(ErrorType.E_6002);
        }

        // 获取买家信息，检查积分是否足够
        UserInfo buyer = userInfoService.selectUserInfoById(buyerId);
        if (buyer == null) {
            throw new ServiceException(ErrorType.E_6003);

        }
        String needSilver = sysConfigService.selectConfigByKey("swap.help.silver");

        Long contactFee = Long.valueOf(needSilver);
        if (buyer.getSilver() < contactFee) {
            throw new ServiceException(ErrorType.E_6004);
        }

        // 创建订单
        TradeOrder order = new TradeOrder();
        // 生成订单号
        String orderNo = OrderUtils.generateOrderNo();
        order.setOrderNo(orderNo);
        order.setOrderType(OrderType.COMMUNITY_HELP.val());
        order.setBuyerId(buyerId);
        order.setSellerId(communityHelp.getUserId());
        order.setBusinessId(id);
        order.setPoints(contactFee);
        order.setStatus(OrderStatus.CONTACT.val());

        int insertResult = tradeOrderService.insertTradeOrder(order);
        if (insertResult <= 0) {
            throw new ServiceException(ErrorType.E_500);
        }

        // 扣除联系费用
        userInfoService.deductSilverAndRecord(buyerId, contactFee, SilverEventType.COMMUNITY_HELP_CONTACT);

        // 更新互助信息状态为已售
        communityHelp.setStatus(ProductStatus.SOLD.getCode());
        communityHelp.setBuyerId(buyerId);
        communityHelpService.updateCommunityHelp(communityHelp);

        Map<String, Object> resultData = new HashMap<>();
        resultData.put("orderId", order.getId());
        return AjaxResult.success("联系成功", resultData);
    }
}