package com.fs.swap.wx.controller;

import com.fs.swap.common.core.controller.WxApiBaseController;
import com.fs.swap.common.core.domain.AjaxResult;
import com.fs.swap.common.core.domain.entity.Activity;
import com.fs.swap.common.core.domain.entity.ActivitySignup;
import com.fs.swap.common.core.domain.entity.UserContact;
import com.fs.swap.common.core.domain.entity.UserInfo;
import com.fs.swap.common.core.page.TableDataInfo;
import com.fs.swap.common.enums.ActivityStatus;
import com.fs.swap.common.enums.ErrorType;
import com.fs.swap.common.enums.SignupStatus;
import com.fs.swap.common.enums.TaskEventType;
import com.fs.swap.common.exception.ServiceException;
import com.fs.swap.system.service.IActivityService;
import com.fs.swap.system.service.IActivitySignupService;
import com.fs.swap.system.service.IUserContactService;
import com.fs.swap.system.service.IUserInfoService;
import com.fs.swap.wx.mapstruct.ActivitySignupConvert;
import com.fs.swap.wx.pojo.dto.ActivitySignupDTO;
import com.fs.swap.wx.pojo.vo.ActivitySignupVO;
import com.fs.swap.wx.service.TaskEventService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 活动报名Controller
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@RestController
@RequestMapping("/activity/signup")
public class ActivitySignupController extends WxApiBaseController {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private IActivitySignupService activitySignupService;

    @Autowired
    private IActivityService activityService;

    @Autowired
    private IUserInfoService userInfoService;

    @Autowired
    private IUserContactService userContactService;

    @Autowired
    private ActivitySignupConvert activitySignupConvert;

    @Value("${dromara.fileDomain}")
    private String fileUrl;

    @Autowired
    private TaskEventService taskEventService;

    /**
     * 查询活动报名列表
     */
    @GetMapping("/list/{activityId}")
    public TableDataInfo list(@PathVariable("activityId") Long activityId) {
        // 检查活动是否存在
        Activity activity = activityService.selectActivityById(activityId);
        if (activity == null) {
            throw new ServiceException(ErrorType.E_404);
        }

        // 检查是否是活动发布者
//        Long userId = getUserId();
//        if (!activity.getUserId().equals(userId)) {
//            throw new ServiceException(ErrorType.E_403);
//        }

        startPage();
        ActivitySignup query = new ActivitySignup();
        query.setActivityId(activityId);
        List<ActivitySignup> list = activitySignupService.selectActivitySignupList(query);
        TableDataInfo dataTable = getDataTable(list);

        // 转换为VO
        List<ActivitySignupVO> voList = activitySignupConvert.toVOList(list);

        // 处理头像路径和状态名称
        for (ActivitySignupVO vo : voList) {
            if (vo.getAvatar() != null) {
                vo.setAvatar(fileUrl + vo.getAvatar());
            }

            // 设置状态名称
            SignupStatus status = SignupStatus.getByCode(vo.getStatus());
            if (status != null) {
                vo.setStatusName(status.getInfo());
            }
        }

        dataTable.setRows(voList);
        return dataTable;
    }

    /**
     * 获取活动报名详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        ActivitySignup activitySignup = activitySignupService.selectActivitySignupById(id);
        if (activitySignup == null) {
            throw new ServiceException(ErrorType.E_404);
        }

        // 检查是否是活动发布者或报名用户
        Long userId = getUserId();
        Activity activity = activityService.selectActivityById(activitySignup.getActivityId());
        if (activity == null) {
            throw new ServiceException(ErrorType.E_404);
        }

        if (!activity.getUserId().equals(userId) && !activitySignup.getUserId().equals(userId)) {
            throw new ServiceException(ErrorType.E_403);
        }

        // 转换为VO
        ActivitySignupVO vo = activitySignupConvert.toVO(activitySignup);

        // 处理头像路径和状态名称
        if (vo.getAvatar() != null) {
            vo.setAvatar(fileUrl + vo.getAvatar());
        }

        // 设置状态名称
        SignupStatus status = SignupStatus.getByCode(vo.getStatus());
        if (status != null) {
            vo.setStatusName(status.getInfo());
        }

        return AjaxResult.success(vo);
    }

    /**
     * 报名活动
     */
    @PostMapping
    public AjaxResult signup(@RequestBody @Valid ActivitySignupDTO activitySignupDTO) {
        // 获取当前用户ID
        Long userId = getUserId();

        // 检查活动是否存在
        Activity activity = activityService.selectActivityById(activitySignupDTO.getActivityId());
        if (activity == null) {
            throw new ServiceException(ErrorType.E_404);
        }

        // 检查活动状态是否为发布中
        if (!ActivityStatus.PUBLISHED.getCode().equals(activity.getStatus())) {
            throw new ServiceException("活动不在发布中状态，无法报名");
        }

        // 检查当前时间是否在报名时间范围内
        Date now = new Date();
        if (now.before(activity.getSignupStartTime())) {
            throw new ServiceException("报名未开始");
        }
        if (now.after(activity.getSignupEndTime())) {
            throw new ServiceException("报名已结束");
        }

        // 检查是否已有有效报名
        ActivitySignup existSignup = activitySignupService.selectActivitySignupByActivityIdAndUserId(
                activitySignupDTO.getActivityId(), userId);
        if (existSignup != null &&
                (SignupStatus.PENDING.getCode().equals(existSignup.getStatus()) ||
                        SignupStatus.APPROVED.getCode().equals(existSignup.getStatus()))) {
            throw new ServiceException("您已报名该活动");
        }

        // 检查报名人数是否已满
        if (activity.getMaxParticipants() != null) {
            int signupCount = activitySignupService.getActivitySignupCount(activitySignupDTO.getActivityId());
            if (signupCount >= activity.getMaxParticipants()) {
                throw new ServiceException("报名人数已满");
            }
        }

        int rows;
        if (existSignup != null) {
            // 如果已有报名记录（可能是已取消或已拒绝状态），则更新现有记录
            existSignup.setAdditionalInfo(activitySignupDTO.getAdditionalInfo());
            existSignup.setSignupTime(new Date());
            existSignup.setStatus(SignupStatus.PENDING.getCode());
            existSignup.setUpdateTime(new Date());

            // 更新报名
            rows = activitySignupService.updateActivitySignup(existSignup);

            // 返回成功时使用现有报名ID
            if (rows > 0) {
                return AjaxResult.success(existSignup.getId());
            }
        } else {
            // 如果没有报名记录，则新增报名
            ActivitySignup activitySignup = activitySignupConvert.toEntity(activitySignupDTO);

            // 设置用户ID和报名时间
            activitySignup.setUserId(userId);
            activitySignup.setSignupTime(new Date());
            activitySignup.setStatus(SignupStatus.PENDING.getCode());

            // 新增报名
            rows = activitySignupService.insertActivitySignup(activitySignup);

            // 返回成功时使用新报名ID
            if (rows > 0) {
                // 触发任务事件
                if (activity.getId() != null) {
                    try {
                        // 触发发布商品任务事件
                        taskEventService.processTaskEvent(userId, TaskEventType.ACTIVITY_JOIN.val(), activity.getId().toString());
                        logger.info("触发参加活动发布任务事件，用户ID: {}, 活动ID: {}", userId, activity.getId());
                    } catch (Exception e) {
                        // 任务事件处理失败不影响主业务
                        logger.warn("任务事件处理失败，但不影响活动报名: {}", e.getMessage());
                    }
                }
                return AjaxResult.success(activitySignup.getId());
            }
        }
        return AjaxResult.error();
    }

    /**
     * 取消报名
     */
    @PutMapping("/cancel/{id}")
    public AjaxResult cancel(@PathVariable("id") Long id) {
        // 获取当前用户ID
        Long userId = getUserId();

        // 取消报名
        int rows = activitySignupService.cancelSignup(id, userId);
        if (rows > 0) {
            return AjaxResult.success();
        }
        return AjaxResult.error();
    }

    /**
     * 审核报名
     */
    @PutMapping("/audit/{id}/{status}")
    public AjaxResult audit(@PathVariable("id") Long id, @PathVariable("status") String status) {
        // 获取当前用户ID
        Long userId = getUserId();

        // 审核报名
        int rows = activitySignupService.auditSignup(id, status, userId);
        if (rows > 0) {
            return AjaxResult.success();
        }
        return AjaxResult.error();
    }

    /**
     * 查询用户的报名列表
     */
    @GetMapping("/my")
    public TableDataInfo myList() {
        startPage();
        Long userId = getUserId();
        List<ActivitySignup> list = activitySignupService.selectActivitySignupListByUserId(userId);
        TableDataInfo dataTable = getDataTable(list);

        // 转换为VO
        List<ActivitySignupVO> voList = activitySignupConvert.toVOList(list);

        // 处理状态名称
        for (ActivitySignupVO vo : voList) {
            // 设置状态名称
            SignupStatus status = SignupStatus.getByCode(vo.getStatus());
            if (status != null) {
                vo.setStatusName(status.getInfo());
            }

            // 处理活动图片
            if (vo.getActivityImages() != null) {
                vo.setActivityImages(fileUrl + vo.getActivityImages());
            }
        }

        dataTable.setRows(voList);
        return dataTable;
    }

    /**
     * 获取活动报名者的联系方式
     * 活动发布者可以查看报名者的联系方式，报名者可以查看活动发布者的联系方式
     */
    @GetMapping("/contact/{signupId}")
    public AjaxResult getSignupUserContact(@PathVariable Long signupId) {
        // 获取当前登录用户ID
        Long userId = getUserId();
        logger.info("获取活动报名联系方式请求，报名ID: {}, 用户ID: {}", signupId, userId);

        // 获取报名信息
        ActivitySignup signup = activitySignupService.selectActivitySignupById(signupId);
        if (signup == null) {
            logger.error("报名记录不存在，报名ID: {}", signupId);
            throw new ServiceException(ErrorType.E_404);
        }

        // 获取活动信息
        Activity activity = activityService.selectActivityById(signup.getActivityId());
        if (activity == null) {
            logger.error("活动不存在，活动ID: {}", signup.getActivityId());
            throw new ServiceException(ErrorType.E_404);
        }

        // 检查权限：只有活动发布者或报名者可以查看联系方式
        if (!userId.equals(activity.getUserId()) && !userId.equals(signup.getUserId())) {
            logger.error("无权查看联系方式，用户ID: {}, 活动发布者ID: {}, 报名者ID: {}",
                    userId, activity.getUserId(), signup.getUserId());
            throw new ServiceException(ErrorType.E_403);
        }

        // 根据当前用户角色获取对方的联系方式
        Long targetUserId;
        if (userId.equals(activity.getUserId())) {
            // 当前用户是活动发布者，获取报名者信息
            targetUserId = signup.getUserId();
            logger.info("当前用户是活动发布者，获取报名者信息，报名者ID: {}", targetUserId);
        } else {
            // 当前用户是报名者，获取活动发布者信息
            targetUserId = activity.getUserId();
            logger.info("当前用户是报名者，获取活动发布者信息，发布者ID: {}", targetUserId);
        }

        // 获取目标用户信息
        UserInfo targetUser = userInfoService.selectUserInfoById(targetUserId);
        if (targetUser == null) {
            logger.error("目标用户不存在，用户ID: {}", targetUserId);
            throw new ServiceException(ErrorType.E_404);
        }

        // 获取目标用户的可见联系方式
        List<UserContact> contacts = userContactService.selectVisibleUserContactsByUserId(targetUserId);

        return AjaxResult.success(contacts);
    }
}
